"""
Agent Orchestrator for Mobius AI Assistant
Main coordinator that routes tasks to specialized agents
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from ..agents.base_agent import BaseAgent, AgentResult, agent_registry
from ..memory.memory_manager import memory_manager
from .llm_engine import llm_engine
from ..config.settings import AGENT_CONFIG

logger = logging.getLogger(__name__)

class TaskRequest:
    """Represents a user task request"""
    
    def __init__(self, user_input: str, context: Optional[Dict] = None):
        self.user_input = user_input
        self.context = context or {}
        self.timestamp = datetime.now()
        self.id = f"task_{self.timestamp.strftime('%Y%m%d_%H%M%S')}_{hash(user_input) % 1000}"

class Orchestrator:
    """
    Main orchestrator that coordinates between LLM, agents, and memory
    """
    
    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_history: List[Dict[str, Any]] = []
        self.is_initialized = False
    
    async def initialize(self):
        """Initialize the orchestrator and load model"""
        if not self.is_initialized:
            logger.info("Initializing Mobius Orchestrator...")
            
            # Load LLM model
            if not llm_engine.load_model():
                logger.error("Failed to load LLM model")
                return False
            
            # Initialize any registered agents
            for agent in agent_registry.get_all_agents():
                logger.info(f"Agent available: {agent.name}")
            
            self.is_initialized = True
            logger.info("Orchestrator initialized successfully")
            return True
        return True
    
    async def process_request(self, user_input: str) -> str:
        """
        Main entry point for processing user requests
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            # Create task request
            task_request = TaskRequest(user_input)
            
            # Check for special commands
            if await self._handle_special_commands(user_input):
                return self._get_special_command_response(user_input)
            
            # Analyze intent and determine if agents are needed
            intent_analysis = await self._analyze_intent(user_input)
            
            if intent_analysis["needs_agent"]:
                # Route to appropriate agent
                result = await self._route_to_agent(intent_analysis, task_request)
                response = await self._generate_response_with_agent_result(user_input, result)
            else:
                # Handle with LLM directly
                response = await self._generate_direct_response(user_input)
            
            # Store in memory
            memory_manager.add_conversation(user_input, response)
            
            # Log task
            self.task_history.append({
                "id": task_request.id,
                "user_input": user_input,
                "response": response,
                "timestamp": task_request.timestamp.isoformat(),
                "used_agent": intent_analysis.get("agent_type") if intent_analysis["needs_agent"] else None
            })
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing request: {str(e)}")
            return f"I encountered an error while processing your request: {str(e)}"
    
    async def _analyze_intent(self, user_input: str) -> Dict[str, Any]:
        """
        Analyze user intent to determine if agents are needed
        """
        # Get memory context
        context = memory_manager.get_context_for_llm()
        
        # Create intent analysis prompt
        prompt = f"""
Context: {context}

User request: {user_input}

Analyze this request and determine:
1. Does it need a specialized agent? (web search, file operations, system commands, etc.)
2. What type of agent would be best?
3. What are the key parameters?

Respond in this format:
NEEDS_AGENT: yes/no
AGENT_TYPE: web_scraper/file_manager/system_controller/code_assistant/none
TASK_TYPE: specific_task_name
PARAMETERS: key parameters as JSON
CONFIDENCE: 0.0-1.0

Analysis:"""
        
        try:
            analysis_response = llm_engine.generate_response(prompt, max_new_tokens=200)
            
            # Parse the response (simple parsing for now)
            lines = analysis_response.split('\n')
            result = {
                "needs_agent": False,
                "agent_type": None,
                "task_type": "conversation",
                "parameters": {},
                "confidence": 0.5
            }
            
            for line in lines:
                if line.startswith("NEEDS_AGENT:"):
                    result["needs_agent"] = "yes" in line.lower()
                elif line.startswith("AGENT_TYPE:"):
                    agent_type = line.split(":", 1)[1].strip()
                    if agent_type != "none":
                        result["agent_type"] = agent_type
                elif line.startswith("TASK_TYPE:"):
                    result["task_type"] = line.split(":", 1)[1].strip()
                elif line.startswith("CONFIDENCE:"):
                    try:
                        result["confidence"] = float(line.split(":", 1)[1].strip())
                    except:
                        pass
            
            return result
            
        except Exception as e:
            logger.error(f"Intent analysis failed: {str(e)}")
            # Fallback to simple keyword detection
            return self._simple_intent_detection(user_input)
    
    def _simple_intent_detection(self, user_input: str) -> Dict[str, Any]:
        """Simple fallback intent detection"""
        user_lower = user_input.lower()
        
        # Web search keywords
        if any(keyword in user_lower for keyword in ["search", "lookup", "find online", "google", "web"]):
            return {
                "needs_agent": True,
                "agent_type": "web_scraper",
                "task_type": "web_search",
                "parameters": {"query": user_input},
                "confidence": 0.8
            }
        
        # File operations
        if any(keyword in user_lower for keyword in ["file", "folder", "directory", "save", "read", "write"]):
            return {
                "needs_agent": True,
                "agent_type": "file_manager",
                "task_type": "file_operation",
                "parameters": {"operation": user_input},
                "confidence": 0.7
            }
        
        # System commands
        if any(keyword in user_lower for keyword in ["run", "execute", "command", "system", "process"]):
            return {
                "needs_agent": True,
                "agent_type": "system_controller",
                "task_type": "system_command",
                "parameters": {"command": user_input},
                "confidence": 0.6
            }
        
        # Default to conversation
        return {
            "needs_agent": False,
            "agent_type": None,
            "task_type": "conversation",
            "parameters": {},
            "confidence": 0.9
        }
    
    async def _route_to_agent(self, intent_analysis: Dict[str, Any], task_request: TaskRequest) -> AgentResult:
        """Route task to appropriate agent"""
        agent_type = intent_analysis["agent_type"]
        task_type = intent_analysis["task_type"]
        
        # Find capable agents
        capable_agents = agent_registry.find_capable_agents(task_type, intent_analysis["parameters"])
        
        if not capable_agents:
            return AgentResult(
                success=False,
                message=f"No agent available for task type: {task_type}"
            )
        
        # Select best agent (for now, just use the first one)
        selected_agent = capable_agents[0]
        
        # Prepare task data
        task_data = {
            "type": task_type,
            "user_input": task_request.user_input,
            "parameters": intent_analysis["parameters"],
            "context": task_request.context
        }
        
        # Execute task
        logger.info(f"Routing task to agent: {selected_agent.name}")
        result = await selected_agent.run_task(task_data)
        
        return result
    
    async def _generate_response_with_agent_result(self, user_input: str, agent_result: AgentResult) -> str:
        """Generate response incorporating agent result"""
        context = memory_manager.get_context_for_llm()
        
        if agent_result.success:
            prompt = f"""
Context: {context}

User asked: {user_input}

Agent successfully completed the task with result:
{agent_result.message}
Data: {agent_result.data}

Provide a helpful response to the user incorporating this information:"""
        else:
            prompt = f"""
Context: {context}

User asked: {user_input}

Agent encountered an issue: {agent_result.message}

Provide a helpful response explaining what happened and suggest alternatives:"""
        
        return llm_engine.generate_response(prompt, max_new_tokens=300)
    
    async def _generate_direct_response(self, user_input: str) -> str:
        """Generate direct response using LLM"""
        context = memory_manager.get_context_for_llm()
        
        prompt = f"""
Context: {context}

User: {user_input}
